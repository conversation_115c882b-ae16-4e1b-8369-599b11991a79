/**
 * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers
 */
export declare const SANCTIONED_UNITS: readonly ["angle-degree", "area-acre", "area-hectare", "concentr-percent", "digital-bit", "digital-byte", "digital-gigabit", "digital-gigabyte", "digital-kilobit", "digital-kilobyte", "digital-megabit", "digital-megabyte", "digital-petabyte", "digital-terabit", "digital-terabyte", "duration-day", "duration-hour", "duration-millisecond", "duration-minute", "duration-month", "duration-second", "duration-week", "duration-year", "length-centimeter", "length-foot", "length-inch", "length-kilometer", "length-meter", "length-mile-scandinavian", "length-mile", "length-millimeter", "length-yard", "mass-gram", "mass-kilogram", "mass-ounce", "mass-pound", "mass-stone", "temperature-celsius", "temperature-fahrenheit", "volume-fluid-ounce", "volume-gallon", "volume-liter", "volume-milliliter"];
export declare function removeUnitNamespace(unit: string): string;
/**
 * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers
 */
export declare const SIMPLE_UNITS: string[];
/**
 * https://tc39.es/ecma402/#sec-issanctionedsimpleunitidentifier
 */
export declare function IsSanctionedSimpleUnitIdentifier(unitIdentifier: string): boolean;
