(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/i18n/messages/en.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_i18n_messages_en_json_94088789._.js",
  "static/chunks/src_i18n_messages_en_json_ae641b12._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/en.json (json)");
    });
});
}}),
"[project]/src/i18n/messages/zh.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_i18n_messages_zh_json_a4b2b6fe._.js",
  "static/chunks/src_i18n_messages_zh_json_ae641b12._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/zh.json (json)");
    });
});
}}),
}]);