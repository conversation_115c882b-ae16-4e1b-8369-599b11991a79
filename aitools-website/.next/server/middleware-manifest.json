{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BOMlhYgrez9cEf44k5/VTirUNPFA7lrGuNb3S5ZTdEM=", "__NEXT_PREVIEW_MODE_ID": "1ad6f08f70aeb28dfa2abac7bf9041cc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8c020d68e80338625d48183c930658990d02692d8f45353c7ea10fcc0883a5bf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "af22e4967be24e7b7831301b2107536c494efa4c27db8c7bd4e351ec762fed58"}}}, "sortedMiddleware": ["/"], "functions": {}}