'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import ToolCard from '@/components/ToolCard';
import ErrorMessage from '@/components/ErrorMessage';
import { Tool } from '@/lib/api';
import { ArrowLeft, Filter, Grid, List, ChevronDown } from 'lucide-react';
import { Locale } from '@/i18n/config';

// 分类信息接口
interface CategoryInfo {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoryPageClientProps {
  categoryInfo: CategoryInfo | null;
  tools: Tool[];
  error: string | null;
}



export default function CategoryPageClient({ categoryInfo, tools, error }: CategoryPageClientProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPricing, setSelectedPricing] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const pathname = usePathname();
  const t = useTranslations('category_page');
  const locale = useLocale() as Locale;

  // Generate localized href
  const getLocalizedHref = (path: string) => {
    return locale === 'zh' ? path : `/en${path}`;
  };

  // Define pricing options with translations
  const pricingOptions = [
    { value: '', label: t('pricing_all') },
    { value: 'free', label: t('pricing_free') },
    { value: 'freemium', label: t('pricing_freemium') },
    { value: 'paid', label: t('pricing_paid') }
  ];

  // Define sort options with translations
  const sortOptions = [
    { value: 'popular', label: t('sort_popular') },
    { value: 'newest', label: t('sort_newest') },
    { value: 'name', label: t('sort_name') },
    { value: 'views', label: t('sort_views') }
  ];

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!categoryInfo) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('not_found_title')}</h3>
          <p className="text-gray-600">{t('not_found_desc')}</p>
          <Link href={getLocalizedHref('/categories')} className="text-blue-600 hover:text-blue-700 mt-4 inline-block">
            {t('back_to_categories')}
          </Link>
        </div>
      </div>
    );
  }

  // Filter and sort tools
  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;
    
    return matchesSearch && matchesPricing;
  });

  const sortedTools = [...filteredTools].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return (b.likes || 0) - (a.likes || 0);
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'name':
        return a.name.localeCompare(b.name);
      case 'newest':
        if (a.createdAt && b.createdAt) {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
        return 0;
      default:
        return 0;
    }
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
        <Link href={getLocalizedHref('/')} className="hover:text-blue-600">{t('breadcrumb_home')}</Link>
        <span>/</span>
        <Link href={getLocalizedHref('/categories')} className="hover:text-blue-600">{t('breadcrumb_categories')}</Link>
        <span>/</span>
        <span className="text-gray-900">{categoryInfo.name}</span>
      </div>

      {/* Back Button */}
      <div className="mb-6">
        <Link
          href={getLocalizedHref('/categories')}
          className="inline-flex items-center text-blue-600 hover:text-blue-700"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back_to_categories')}
        </Link>
      </div>

      {/* Category Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-3xl"
            style={{ backgroundColor: categoryInfo.color }}
          >
            <span className="text-white">
              {categoryInfo.icon}
            </span>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {categoryInfo.name}
            </h1>
            <p className="text-lg text-gray-600">
              {t('tools_count', { count: categoryInfo.toolCount })}
            </p>
          </div>
        </div>
        <p className="text-gray-600 leading-relaxed">
          {categoryInfo.description}
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        {/* Search Bar */}
        <div className="relative mb-4">
          <input
            type="text"
            placeholder={t('search_placeholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <Filter className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
        </div>

        {/* Filter Toggle Button (Mobile) */}
        <div className="md:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          >
            <Filter className="mr-2 h-4 w-4" />
            {t('filter_options')}
            <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters */}
        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('pricing')}</label>
            <select
              value={selectedPricing}
              onChange={(e) => setSelectedPricing(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {pricingOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('sort')}</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('view')}</label>
            <div className="flex rounded-lg border border-gray-300">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Grid className="h-4 w-4 mx-auto" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <List className="h-4 w-4 mx-auto" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="mb-6">
        <p className="text-gray-600">
          {t('results_count', { count: sortedTools.length })}
          {searchTerm && ` ${t('search_for', { term: searchTerm })}`}
        </p>
      </div>

      {/* Tools Grid/List */}
      {sortedTools.length > 0 ? (
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {sortedTools.map((tool) => (
            <ToolCard key={tool._id} tool={tool} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Filter className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('no_results_title')}</h3>
          <p className="text-gray-600">
            {t('no_results_desc')}
          </p>
        </div>
      )}

      {/* Related Categories */}
      <section className="mt-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('related_categories')}</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link
            href="/categories/image-generation"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">🎨</span>
            <span className="text-sm font-medium text-gray-900">{t('image_generation')}</span>
          </Link>
          <Link
            href="/categories/code-generation"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">💻</span>
            <span className="text-sm font-medium text-gray-900">{t('code_generation')}</span>
          </Link>
          <Link
            href="/categories/data-analysis"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">📊</span>
            <span className="text-sm font-medium text-gray-900">{t('data_analysis')}</span>
          </Link>
          <Link
            href="/categories/audio-processing"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">🎵</span>
            <span className="text-sm font-medium text-gray-900">{t('audio_processing')}</span>
          </Link>
        </div>
      </section>
    </div>
  );
}
