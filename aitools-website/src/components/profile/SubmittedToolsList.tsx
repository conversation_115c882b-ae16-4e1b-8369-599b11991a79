'use client';

import React, { useState, useEffect, Fragment } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import { Tool } from '@/lib/api';
import {
  Plus,
  Edit,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  ExternalLink,
  BarChart3,
  ArrowLeft
} from 'lucide-react';

interface SubmittedToolsListProps {}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved':
      return <CheckCircle className="h-4 w-4" />;
    case 'pending':
      return <Clock className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    case 'draft':
      return <Edit className="h-4 w-4" />;
    default:
      return null;
  }
};

export default function SubmittedToolsList({}: SubmittedToolsListProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('profile.submitted');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }
    
    if (status === 'authenticated') {
      fetchTools();
    }
  }, [status, router]);

  const fetchTools = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/user/tools');
      const data = await response.json();

      if (data.success && data.data) {
        setTools(data.data.tools);
      } else {
        setError(data.message || t('error_message'));
      }
    } catch (error) {
      setError(t('network_error'));
    } finally {
      setLoading(false);
    }
  };

  const getStatusText = (status: string) => {
    return t(`status.${status}`);
  };

  const filteredTools = tools.filter(tool =>
    selectedStatus === 'all' || tool.status === selectedStatus
  );

  const stats = {
    total: tools.length,
    draft: tools.filter(t => t.status === 'draft').length,
    approved: tools.filter(t => t.status === 'approved').length,
    pending: tools.filter(t => t.status === 'pending').length,
    rejected: tools.filter(t => t.status === 'rejected').length,
    totalViews: tools.reduce((sum, t) => sum + t.views, 0),
    totalLikes: tools.reduce((sum, t) => sum + t.likes, 0)
  };

  if (status === 'loading' || loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <LoadingSpinner size="lg" className="py-20" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <Fragment>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <div className="flex items-center mb-2">
              <Link
                href={`/${locale}/profile`}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">{t('title')}</h1>
            </div>
            <p className="text-lg text-gray-600">{t('subtitle')}</p>
          </div>
          <Link
            href={`/${locale}/submit`}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Plus className="mr-2 h-5 w-5" />
            {t('submit_new_tool')}
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.total_submissions')}</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.approved')}</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Eye className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.total_views')}</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 text-red-600">❤️</div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.total_likes')}</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalLikes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedStatus('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('filters.all')} ({stats.total})
            </button>
            <button
              onClick={() => setSelectedStatus('draft')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'draft'
                  ? 'bg-gray-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('filters.draft')} ({stats.draft})
            </button>
            <button
              onClick={() => setSelectedStatus('approved')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'approved'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('filters.approved')} ({stats.approved})
            </button>
            <button
              onClick={() => setSelectedStatus('pending')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'pending'
                  ? 'bg-yellow-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('filters.pending')} ({stats.pending})
            </button>
            <button
              onClick={() => setSelectedStatus('rejected')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'rejected'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('filters.rejected')} ({stats.rejected})
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Tools List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredTools.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredTools.map((tool) => (
                <div key={tool._id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {tool.name}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)}`}>
                          {getStatusIcon(tool.status)}
                          <span className="ml-1">{getStatusText(tool.status)}</span>
                        </span>
                      </div>

                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {tool.description}
                      </p>

                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{t('dates.submitted_on')} {new Date(tool.submittedAt).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
                        </div>
                        {tool.launchDate && (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-4 w-4" />
                            <span>{t('dates.published_on')} {new Date(tool.launchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
                          </div>
                        )}
                        {tool.status === 'approved' && (
                          <>
                            <div className="flex items-center space-x-1">
                              <Eye className="h-4 w-4" />
                              <span>{tool.views} {t('metrics.views')}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <span>❤️</span>
                              <span>{tool.likes} {t('metrics.likes')}</span>
                            </div>
                          </>
                        )}
                      </div>

                      {tool.status === 'rejected' && tool.reviewNotes && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-800">
                            <strong>{t('rejection.reason')}</strong> {tool.reviewNotes}
                          </p>
                        </div>
                      )}

                      {tool.status === 'draft' && (
                        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <p className="text-sm text-blue-800 mb-2">
                            <strong>{t('next_steps.select_launch_date')}</strong>
                          </p>
                          <Link
                            href={`/${locale}/submit/launch-date/${tool._id}`}
                            className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            {t('actions.set_launch_date')}
                          </Link>
                        </div>
                      )}

                      {tool.status === 'pending' && tool.launchOption && (
                        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="text-sm text-yellow-800">
                            <div className="flex justify-between items-center mb-1">
                              <span><strong>{t('next_steps.launch_option')}</strong></span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                tool.launchOption === 'paid'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {t(`launch_options.${tool.launchOption}`)}
                              </span>
                            </div>
                            {tool.selectedLaunchDate && (
                              <div className="flex justify-between items-center mb-1">
                                <span><strong>{t('dates.planned_launch')}</strong></span>
                                <span>{new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
                              </div>
                            )}
                            {tool.paymentRequired && (
                              <div className="flex justify-between items-center mb-2">
                                <span><strong>{t('next_steps.payment_status')}</strong></span>
                                <span className={`px-2 py-1 rounded text-xs ${
                                  tool.paymentStatus === 'completed'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {t(`payment_status.${tool.paymentStatus}`)}
                                </span>
                              </div>
                            )}
                            <div className="flex justify-end mt-2">
                              <Link
                                href={`/${locale}/submit/edit-launch-date/${tool._id}`}
                                className="inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors"
                              >
                                <Calendar className="h-3 w-3 mr-1" />
                                {t('actions.modify_launch_date')}
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 已通过的工具显示launch date信息 */}
                      {tool.status === 'approved' && tool.launchOption && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="text-sm text-green-800">
                            <div className="flex justify-between items-center mb-1">
                              <span><strong>{t('next_steps.launch_option')}</strong></span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                tool.launchOption === 'paid'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {t(`launch_options.${tool.launchOption}`)}
                              </span>
                            </div>
                            {tool.selectedLaunchDate && (
                              <div className="flex justify-between items-center">
                                <span><strong>{t('dates.launch_date')}</strong></span>
                                <span>{new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {tool.status === 'approved' && (
                        <Link
                          href={`/${locale}/tools/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title={t('tooltips.view_details')}
                        >
                          <Eye className="h-5 w-5" />
                        </Link>
                      )}

                      {/* Launch Date 管理按钮 */}
                      {tool.status === 'draft' && !tool.launchDateSelected && (
                        <Link
                          href={`/${locale}/submit/launch-date/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title={t('tooltips.set_launch_date')}
                        >
                          <Calendar className="h-5 w-5" />
                        </Link>
                      )}

                      {['pending', 'approved'].includes(tool.status) && tool.launchDateSelected && (
                        <Link
                          href={`/${locale}/submit/edit-launch-date/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-orange-600 transition-colors"
                          title={t('tooltips.modify_launch_date')}
                        >
                          <Calendar className="h-5 w-5" />
                        </Link>
                      )}

                      <a
                        href={tool.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                        title={t('tooltips.visit_website')}
                      >
                        <ExternalLink className="h-5 w-5" />
                      </a>
                      {['draft', 'pending', 'rejected', 'approved', 'published'].includes(tool.status) && (
                        <Link
                          href={`/${locale}/submit/edit/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title={
                            (tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date())
                              ? t('tooltips.edit_basic_info')
                              : tool.status === 'approved'
                              ? t('tooltips.edit_basic_info_no_url')
                              : t('tooltips.edit_tool_info')
                          }
                        >
                          <Edit className="h-5 w-5" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <BarChart3 className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedStatus === 'all' ? t('empty_states.no_tools') : t('empty_states.no_status_tools', { status: getStatusText(selectedStatus) })}
              </h3>
              <p className="text-gray-600 mb-4">
                {selectedStatus === 'all'
                  ? t('empty_states.get_started')
                  : t('empty_states.try_other_status')
                }
              </p>
              {selectedStatus === 'all' && (
                <Link
                  href={`/${locale}/submit`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {t('submit_new_tool')}
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </Fragment>
  );
}
