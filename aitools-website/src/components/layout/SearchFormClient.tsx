'use client';

import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { FaSearch } from 'react-icons/fa';

interface SearchFormClientProps {
  locale: string;
  className?: string;
}

export default function SearchFormClient({ locale, className = "" }: SearchFormClientProps) {
  const router = useRouter();
  const t = useTranslations('navigation');

  // Generate localized href
  const getLocalizedHref = (path: string) => {
    return locale === 'zh' ? path : `/en${path}`;
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const query = formData.get('search') as string;
    if (query.trim()) {
      router.push(getLocalizedHref(`/search?q=${encodeURIComponent(query.trim())}`));
    }
  };

  return (
    <form onSubmit={handleSearch} className={className}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FaSearch className="text-gray-400" />
        </div>
        <input
          name="search"
          type="text"
          placeholder={t('search_placeholder')}
          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>
    </form>
  );
}
