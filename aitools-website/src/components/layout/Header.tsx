import NextLink from 'next/link';
import { getTranslations, getLocale } from 'next-intl/server';
import { type Locale } from '@/i18n/config';
import UserMenuClient from '../auth/UserMenuClient';
import LanguageSwitcherClient from './LanguageSwitcherClient';
import MobileMenuClient from './MobileMenuClient';
import SearchFormClient from './SearchFormClient';

const NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
  <NextLink
    href={href}
    className="px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors"
  >
    {children}
  </NextLink>
);

export default async function Header() {
  const t = await getTranslations('navigation');
  const locale = await getLocale() as Locale;

  // Generate navigation links with current locale
  const getLocalizedHref = (path: string) => {
    return locale === 'zh' ? path : `/en${path}`;
  };

  const links = [
    { name: t('home'), href: getLocalizedHref('/') },
    { name: t('tools'), href: getLocalizedHref('/tools') },
    { name: t('categories'), href: getLocalizedHref('/categories') },
    { name: t('submit'), href: getLocalizedHref('/submit') },
  ];

  return (
    <header className="bg-white px-4 shadow-sm border-b border-gray-200">
      <div className="flex h-16 items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-8">
          <NextLink href={getLocalizedHref('/')} className="flex items-center space-x-2 hover:no-underline">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <span className="text-xl font-bold text-gray-900">
              {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}
            </span>
          </NextLink>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-4">
            {links.map((link) => (
              <NavLink key={link.name} href={link.href}>
                {link.name}
              </NavLink>
            ))}
          </nav>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-md mx-8 hidden md:block">
          <SearchFormClient locale={locale} />
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2">
          {/* Language Switcher */}
          <LanguageSwitcherClient currentLocale={locale} />

          {/* User Menu */}
          <UserMenuClient />

          {/* Mobile Menu */}
          <MobileMenuClient links={links} locale={locale} />
        </div>
      </div>
    </header>
  );
}
