import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

// This is the root layout that must contain html and body tags
// The actual internationalized layout is in [locale]/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
