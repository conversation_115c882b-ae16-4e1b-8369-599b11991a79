import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { Users, Target, Zap, Heart } from 'lucide-react';

// 生成静态metadata
export const metadata: Metadata = {
  title: '关于我们 - AI工具导航',
  description: 'AI工具导航致力于为用户发现和推荐最优质的人工智能工具，帮助提升工作效率和创造力。了解我们的使命、愿景和团队。',
  keywords: '关于我们,AI工具导航,人工智能工具,团队介绍,使命愿景',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/about`,
    siteName: 'AI工具导航',
    title: '关于我们 - AI工具导航',
    description: 'AI工具导航致力于为用户发现和推荐最优质的人工智能工具，帮助提升工作效率和创造力。',
    images: [
      {
        url: '/og-about.jpg',
        width: 1200,
        height: 630,
        alt: '关于我们 - AI工具导航',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '关于我们 - AI工具导航',
    description: 'AI工具导航致力于为用户发现和推荐最优质的人工智能工具。',
    images: ['/og-about.jpg'],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/about`,
  },
};

export default function AboutPage() {
  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: '关于我们', url: '/about' }
  ]);

  // 生成组织结构化数据
  const organizationStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AI工具导航",
    "description": "专业的AI工具发现和推荐平台",
    "url": process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
    "logo": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/logo.png`,
    "foundingDate": "2024",
    "mission": "帮助用户发现和使用最优质的人工智能工具，提升工作效率和创造力",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">关于我们</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            关于 AI工具导航
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们致力于为用户发现和推荐最优质的人工智能工具，帮助每个人在AI时代提升工作效率和创造力。
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
          <div className="bg-blue-50 rounded-lg p-8">
            <div className="flex items-center mb-4">
              <Target className="h-8 w-8 text-blue-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">我们的使命</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              让每个人都能轻松发现和使用最适合的AI工具，无论是个人用户还是企业团队，都能在AI技术的帮助下实现更高的效率和更好的成果。
            </p>
          </div>

          <div className="bg-green-50 rounded-lg p-8">
            <div className="flex items-center mb-4">
              <Zap className="h-8 w-8 text-green-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">我们的愿景</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              成为全球领先的AI工具发现平台，构建一个连接AI工具开发者和用户的桥梁，推动人工智能技术的普及和应用。
            </p>
          </div>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">我们的价值观</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">用户至上</h3>
              <p className="text-gray-600">
                始终以用户需求为中心，提供最有价值的AI工具推荐和使用指导。
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Heart className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">品质保证</h3>
              <p className="text-gray-600">
                严格筛选每一个推荐的AI工具，确保质量和实用性，为用户节省时间。
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">持续创新</h3>
              <p className="text-gray-600">
                紧跟AI技术发展趋势，不断优化平台功能，提供更好的用户体验。
              </p>
            </div>
          </div>
        </div>

        {/* Team */}
        <div className="bg-gray-50 rounded-lg p-8 mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">我们的团队</h2>
          <p className="text-lg text-gray-700 text-center max-w-3xl mx-auto">
            我们是一支充满激情的团队，由AI技术专家、产品设计师和内容策划师组成。
            我们深度了解AI技术发展趋势，致力于为用户提供最前沿、最实用的AI工具推荐。
          </p>
        </div>

        {/* Contact */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">联系我们</h2>
          <p className="text-lg text-gray-600 mb-8">
            如果您有任何问题、建议或合作意向，欢迎随时与我们联系。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              发送邮件
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              联系表单
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
}
