import { Fragment } from 'react';
import { Upload } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import SubmitForm from '@/components/submit/SubmitForm';

export default async function SubmitPage() {
  const t = await getTranslations('submit');

  return (
    <Fragment>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Upload className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600">
            {t('subtitle')}
          </p>
        </div>

        {/* Submit Form Component */}
        <SubmitForm />
      </div>
    </Fragment>
  );
}