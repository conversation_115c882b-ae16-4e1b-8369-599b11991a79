'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import LaunchDateSelector from '@/components/LaunchDateSelector';
import { ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react';

export default function EditLaunchDatePage() {
  const params = useParams();
  const router = useRouter();
  const { status } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = params.toolId as string;



  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`);
      const data = await response.json();
      
      if (data.success) {
        const toolData = data.data.tool;
        setTool(toolData);
        
        // 检查权限和状态
        // 注意：这里应该检查用户ID而不是email，但由于session中没有ID，暂时跳过这个检查
        // 实际的权限检查会在API层面进行

        if (!['pending', 'approved'].includes(toolData.status)) {
          setError('当前状态不允许修改发布日期');
          return;
        }

        // 检查是否已经发布
        const now = new Date();
        if (toolData.selectedLaunchDate && new Date(toolData.selectedLaunchDate) <= now && toolData.status === 'approved') {
          setError('工具已发布，无法修改发布日期');
          return;
        }


      } else {
        setError('工具不存在');
      }
    } catch {
      setError('获取工具信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      // 如果选择了付费选项且当前是免费用户，需要创建新的订单
      if (option === 'paid' && tool?.launchOption === 'free') {
        // 使用原始的POST方法创建付费订单
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            launchOption: 'paid',
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          // 跳转到支付页面
          router.push(data.data.paymentUrl);
        } else {
          setError(data.message || '创建订单失败');
        }
      } else {
        // 使用PATCH方法更新日期
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push(`/submit/success?toolId=${toolId}&paid=${option === 'paid'}`);
        } else {
          setError(data.message || '修改失败');
        }
      }
    } catch {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">出错了</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回上一页
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            修改发布日期
          </h1>
          <p className="text-gray-600">
            您可以随时修改工具的发布日期，直到工具正式发布
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {tool.name}
            </h2>
            <p className="text-gray-600 mb-4">{tool.description}</p>

            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-600">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                当前方案：{tool.launchOption === 'paid' ? '优先发布服务' : '免费发布服务'}
              </div>

              {tool.selectedLaunchDate && (
                <div className="text-sm text-gray-600">
                  当前发布日期：<span className="font-medium">
                    {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Launch Date Selector */}
        {tool && (
          <LaunchDateSelector
            toolId={toolId}
            currentOption={tool.launchOption}
            currentDate={tool.selectedLaunchDate ? new Date(tool.selectedLaunchDate).toISOString().split('T')[0] : undefined}
            isEditing={true}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={error}
          />
        )}
      </div>
    </Layout>
  );
}
