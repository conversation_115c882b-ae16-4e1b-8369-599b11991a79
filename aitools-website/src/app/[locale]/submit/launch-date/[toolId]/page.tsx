'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import LaunchDateSelector from '@/components/LaunchDateSelector';
import { AlertCircle, CheckCircle } from 'lucide-react';



export default function LaunchDatePage() {
  const params = useParams();
  const router = useRouter();
  const { status } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = params.toolId as string;

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}`);
      const data = await response.json();
      
      if (data.success) {
        setTool(data.data);
        // 检查工具是否属于当前用户 - 跳过ID检查，在API层面处理
        // 检查工具状态
        if (data.data.status !== 'draft') {
          setError('此工具已经选择了发布日期');
          return;
        }
      } else {
        setError('工具不存在');
      }
    } catch {
      setError('获取工具信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          launchOption: option,
          selectedDate,
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (option === 'paid' && data.data.paymentUrl) {
          // 跳转到支付页面
          window.location.href = data.data.paymentUrl;
        } else {
          // 免费选项，直接进入审核
          router.push(`/submit/success?toolId=${toolId}`);
        }
      } else {
        setError(data.message || '提交失败');
      }
    } catch {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">出错了</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/submit')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回提交页面
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            工具信息提交成功！
          </h1>
          <p className="text-lg text-gray-600">
            现在请选择您的发布日期和选项
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {tool.name}
            </h2>
            <p className="text-gray-600">{tool.description}</p>
          </div>
        )}

        {/* Launch Date Selector */}
        {tool && (
          <LaunchDateSelector
            toolId={toolId}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={error}
          />
        )}
      </div>
    </Layout>
  );
}
