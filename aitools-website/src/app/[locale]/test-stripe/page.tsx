'use client';

import { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { formatPrice, PRICING_CONFIG } from '@/constants/pricing';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/test-stripe`,
      },
      redirect: 'if_required'
    });

    if (error) {
      setMessage(error.message || '支付失败');
    } else {
      setMessage('支付成功！');
    }

    setIsProcessing(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <PaymentElement />
      <button
        disabled={!stripe || !elements || isProcessing}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50"
      >
        {isProcessing ? '处理中...' : `支付 ${formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}`}
      </button>
      {message && <div className="text-center text-sm">{message}</div>}
    </form>
  );
}

export default function TestStripePage() {
  const [clientSecret, setClientSecret] = useState('');
  const [loading, setLoading] = useState(false);

  const createTestPayment = async () => {
    setLoading(true);
    try {
      // 这里需要创建一个测试订单
      const response = await fetch('/api/test/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount,
          currency: 'cny'
        }),
      });

      const data = await response.json();
      if (data.success) {
        setClientSecret(data.clientSecret);
      } else {
        alert('创建支付失败: ' + data.message);
      }
    } catch (error) {
      alert('创建支付失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6 text-center">Stripe 支付测试</h1>
      
      {!clientSecret ? (
        <div className="text-center">
          <button
            onClick={createTestPayment}
            disabled={loading}
            className="bg-green-600 text-white py-2 px-4 rounded disabled:opacity-50"
          >
            {loading ? '创建中...' : '创建测试支付'}
          </button>
          <p className="text-sm text-gray-600 mt-2">
            点击创建一个 {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)} 的测试支付
          </p>
        </div>
      ) : (
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret,
            appearance: {
              theme: 'stripe',
            }
          }}
        >
          <CheckoutForm />
        </Elements>
      )}
      
      <div className="mt-6 p-4 bg-gray-100 rounded text-sm">
        <h3 className="font-semibold mb-2">测试卡号:</h3>
        <ul className="space-y-1 text-gray-600">
          <li>成功: 4242 4242 4242 4242</li>
          <li>失败: 4000 0000 0000 0002</li>
          <li>需要验证: 4000 0025 0000 3155</li>
        </ul>
        <p className="mt-2 text-xs">
          使用任意未来日期作为过期时间，任意3位数作为CVC
        </p>
      </div>
    </div>
  );
}
