'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import { apiClient, Tool } from '@/lib/api';
import { Locale } from '@/i18n/config';
import {
  ArrowLeft,
  ExternalLink,
  Calendar,
  User,
  Tag,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Globe,
  DollarSign
} from 'lucide-react';



export default function AdminToolDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const toolId = params?.id as string;

  const t = useTranslations('admin');
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (params?.id) {
      fetchTool(params.id as string);
    }
  }, [params?.id]);

  const fetchTool = async (id: string) => {
    try {
      setLoading(true);
      setError('');

      const response = await apiClient.getTool(id);

      if (response.success && response.data) {
        setTool(response.data);
      } else {
        setError(response.error || t('errors.fetch_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!tool) return;

    setIsProcessing(true);
    try {
      setError('');

      const response = await apiClient.approveTool(tool._id, {
        reviewedBy: 'admin', // In production, this should be the current logged-in admin
        reviewNotes: t('success.tool_approved'),
        launchDate: new Date().toISOString()
      });

      if (response.success) {
        setSuccessMessage(t('success.tool_approved'));
        // Refetch tool data to update status
        await fetchTool(tool._id);
      } else {
        setError(response.error || t('errors.approve_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!tool) return;

    if (!rejectReason.trim()) {
      setError(t('errors.reject_reason_required'));
      return;
    }

    setIsProcessing(true);
    try {
      setError('');

      const response = await apiClient.rejectTool(tool._id, {
        reviewedBy: 'admin', // In production, this should be the current logged-in admin
        rejectReason: rejectReason
      });

      if (response.success) {
        setSuccessMessage(t('success.tool_rejected'));
        setShowRejectModal(false);
        setRejectReason('');
        // Refetch tool data to update status
        await fetchTool(tool._id);
      } else {
        setError(response.error || t('errors.reject_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {
    // Check if published: approved status and launchDate has passed
    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();

    if (isPublished) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-4 h-4 mr-2" />
          {t('status_labels.published')}
        </span>
      );
    }

    switch (tool.status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-4 h-4 mr-2" />
            {t('status_labels.pending')}
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            <CheckCircle className="w-4 h-4 mr-2" />
            {t('status_labels.approved')}
          </span>
        );

      case 'rejected':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-2" />
            {t('status_labels.rejected')}
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadingSpinner size="lg" className="py-20" />
        </div>
      </Layout>
    );
  }

  if (error || !tool) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {error || t('errors.tool_not_found')}
            </h3>
            <div className="mt-6">
              <button
                onClick={() => router.push('/admin')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t('actions.back_to_admin')}
              </button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success/Error Messages */}
        {successMessage && (
          <SuccessMessage
            message={successMessage}
            onClose={() => setSuccessMessage('')}
            className="mb-6"
          />
        )}

        {error && (
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('actions.back_to_review')}
          </button>
          
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-6">
              <img
                src={tool.logo}
                alt={tool.name}
                className="w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"
              />
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
                  {getStatusBadge(tool)}
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {t(`category_labels.${tool.category}`) || tool.category}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <DollarSign className="w-3 h-3 mr-1" />
                    {t(`pricing_labels.${tool.pricing}`) || tool.pricing}
                  </span>
                  <a
                    href={tool.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    <Globe className="w-4 h-4 mr-1" />
                    {t('actions.visit_website')}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </div>
                <p className="text-gray-600 max-w-3xl">{tool.tagline}</p>
              </div>
            </div>

            {/* Action Buttons */}
            {tool.status === 'pending' && (
              <div className="flex space-x-3">
                <button
                  onClick={handleApprove}
                  disabled={isProcessing}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {isProcessing ? t('actions.processing') : t('actions.approve')}
                </button>
                <button
                  onClick={() => setShowRejectModal(true)}
                  disabled={isProcessing}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  {t('actions.reject')}
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.tool_description')}</h2>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {tool.description}
                </p>
                {tool.longDescription && (
                  <div className="mt-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('sections.detailed_description')}</h3>
                    <p className="text-gray-700 leading-relaxed">
                      {tool.longDescription}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.tags')}</h2>
              <div className="flex flex-wrap gap-2">
                {tool.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Screenshots */}
            {tool.screenshots && tool.screenshots.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.screenshot_preview')}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {tool.screenshots.map((screenshot, index) => (
                    <img
                      key={index}
                      src={screenshot}
                      alt={`${tool.name} ${t('sections.screenshot_preview')} ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg border border-gray-200"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Submission Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('sections.submission_info')}</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <User className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{tool.submittedBy}</div>
                    <div className="text-sm text-gray-500">{t('fields.submitter_id')}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{formatDate(tool.submittedAt)}</div>
                    <div className="text-sm text-gray-500">{t('fields.submission_time')}</div>
                  </div>
                </div>
                
                {tool.selectedLaunchDate && (
                  <div className="flex items-center">
                    <Clock className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
                      </div>
                      <div className="text-sm text-gray-500">{t('fields.selected_launch_date')}</div>
                    </div>
                  </div>
                )}

                {tool.launchDate && (
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(tool.launchDate).toLocaleDateString('zh-CN')}
                      </div>
                      <div className="text-sm text-gray-500">{t('fields.actual_launch_date')}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Review Guidelines */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-blue-900 mb-2">{t('sections.review_guidelines')}</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• {t('guidelines.verify_website')}</li>
                    <li>• {t('guidelines.check_description')}</li>
                    <li>• {t('guidelines.confirm_category')}</li>
                    <li>• {t('guidelines.evaluate_quality')}</li>
                    <li>• {t('guidelines.check_duplicates')}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('reject_modal.title')}</h3>
              <p className="text-sm text-gray-600 mb-4">
                {t('reject_modal.description')}
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={t('reject_modal.placeholder')}
              />
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                  }}
                  disabled={isProcessing}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {t('actions.cancel')}
                </button>
                <button
                  onClick={handleReject}
                  disabled={!rejectReason.trim() || isProcessing}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {isProcessing ? t('actions.processing') : t('actions.confirm_reject')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
