import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// POST /api/admin/tools/[id]/approve - 批准工具
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const body = await request.json();
    const locale = getLocaleFromRequest(request);

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查工具状态
    if (tool.status !== 'pending') {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.approve_failed') },
        { status: 400 }
      );
    }

    // 更新工具状态为approved，等待发布日期到达
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      {
        $set: {
          status: 'approved',
          reviewedAt: new Date(),
          reviewedBy: body.reviewedBy || 'admin', // 后续应该使用实际的管理员ID
          reviewNotes: body.reviewNotes || '',
          isActive: true
        },
        $unset: {
          // 清除之前的拒绝信息
          rejectedAt: 1,
          rejectReason: 1
        }
      },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: getApiMessage(locale, 'tools.approve_success')
    });

  } catch (error) {
    console.error('Error approving tool:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'tools.approve_failed') },
      { status: 500 }
    );
  }
}
