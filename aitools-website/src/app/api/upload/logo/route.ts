import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

export async function POST(request: NextRequest) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('logo') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'upload.no_file') },
        { status: 400 }
      );
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'upload.invalid_type') },
        { status: 400 }
      );
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'upload.file_too_large') },
        { status: 400 }
      );
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop();
    const fileName = `logo_${timestamp}_${randomString}.${fileExtension}`;

    // 确保上传目录存在
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'logos');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 保存文件
    const filePath = join(uploadDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);

    // 返回文件URL
    const fileUrl = `/uploads/logos/${fileName}`;

    return NextResponse.json({
      success: true,
      data: {
        url: fileUrl,
        filename: fileName,
        size: file.size,
        type: file.type
      },
      message: getApiMessage(locale, 'upload.upload_success')
    });

  } catch (error) {
    console.error('Upload error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'upload.upload_failed') },
      { status: 500 }
    );
  }
}
