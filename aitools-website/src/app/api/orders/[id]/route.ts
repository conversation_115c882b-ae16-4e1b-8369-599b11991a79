import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// GET /api/orders/[id] - 获取订单信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const order = await Order.findById(id).populate('toolId', 'name description');
    if (!order) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.not_found') },
        { status: 404 }
      );
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        _id: order._id,
        type: order.type,
        amount: order.amount,
        currency: order.currency,
        status: order.status,
        description: order.description,
        selectedLaunchDate: order.selectedLaunchDate,
        createdAt: order.createdAt,
        paidAt: order.paidAt,
        tool: order.toolId,
        toolId: order.toolId
      }
    });

  } catch (error) {
    console.error('Get order error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
