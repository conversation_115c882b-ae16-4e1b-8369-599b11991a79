import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// POST /api/orders/[id]/pay - 处理订单支付
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { paymentMethod } = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const order = await Order.findById(id);
    if (!order) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.not_found') },
        { status: 404 }
      );
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'payment.payment_failed') },
        { status: 400 }
      );
    }

    // 注意：这个API现在主要用于向后兼容
    // 新的支付流程使用Stripe Webhook处理支付确认
    // 这里只是检查支付状态或处理特殊情况

    // 如果有Stripe支付意图ID，检查其状态
    if (order.stripePaymentIntentId) {
      try {
        const { stripe } = await import('@/lib/stripe');
        const paymentIntent = await stripe.paymentIntents.retrieve(order.stripePaymentIntentId);

        if (paymentIntent.status === 'succeeded') {
          // 支付已成功，更新订单状态（如果还没更新的话）
          if (order.status !== 'completed') {
            await order.markAsPaid();
            order.paymentMethod = 'stripe';
            await order.save();

            // 更新工具状态
            await Tool.findByIdAndUpdate(order.toolId, {
              $set: {
                paymentStatus: 'completed',
                paidAt: new Date(),
                status: 'pending' // 进入审核队列
              }
            });
          }

          return NextResponse.json({
            success: true,
            data: {
              orderId: order._id,
              message: getApiMessage(locale, 'payment.payment_success')
            }
          });
        } else {
          return NextResponse.json(
            { success: false, message: getApiMessage(locale, 'payment.payment_pending') },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error('Error checking payment intent:', error);
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'payment.status_check_failed') },
          { status: 500 }
        );
      }
    }

    // 如果没有Stripe支付意图ID，返回错误
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'payment.invalid_method') },
      { status: 400 }
    );

  } catch (error) {
    console.error('Payment processing error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'payment.payment_failed') },
      { status: 500 }
    );
  }
}
