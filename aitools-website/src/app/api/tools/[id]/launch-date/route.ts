import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import Order from '@/models/Order';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import { PRICING_CONFIG } from '@/constants/pricing';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// POST /api/tools/[id]/launch-date - 设置工具发布日期和选项
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { launchOption, selectedDate } = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 验证输入
    if (!launchOption || !selectedDate) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    if (!['free', 'paid'].includes(launchOption)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查工具状态 - 允许draft状态或者免费用户升级到付费
    if (tool.status !== 'draft' && !(tool.status === 'pending' && tool.launchOption === 'free' && launchOption === 'paid')) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.launch_date_already_set') },
        { status: 400 }
      );
    }

    const selectedLaunchDate = new Date(selectedDate);
    const now = new Date();

    // 验证日期
    if (launchOption === 'free') {
      // 免费选项：必须是一个月后或更晚的日期
      const oneMonthLater = new Date();
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
      oneMonthLater.setHours(0, 0, 0, 0);

      const selectedDateOnly = new Date(selectedLaunchDate);
      selectedDateOnly.setHours(0, 0, 0, 0);

      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'tools.free_date_restriction') },
          { status: 400 }
        );
      }
    } else {
      // 付费选项：必须是明天或以后的日期
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      if (selectedLaunchDate < tomorrow) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'tools.paid_date_restriction') },
          { status: 400 }
        );
      }
    }

    if (launchOption === 'free') {
      // 免费选项：直接更新工具状态并进入审核
      await Tool.findByIdAndUpdate(id, {
        $set: {
          launchDateSelected: true,
          selectedLaunchDate,
          launchDate: selectedLaunchDate, // 同时设置launchDate
          launchOption: 'free',
          paymentRequired: false,
          status: 'pending' // 进入审核队列
        }
      });

      return NextResponse.json({
        success: true,
        data: {
          message: getApiMessage(locale, 'tools.launch_date_set_success')
        }
      });

    } else {
      // 付费选项：创建订单
      const paymentAmount = PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount;

      const order = new Order({
        userId: user._id,
        toolId: id,
        type: 'launch_date_priority',
        amount: paymentAmount,
        currency: PRICING_CONFIG.PRIORITY_LAUNCH.currency,
        status: 'pending',
        description: `工具 "${tool.name}" 优先发布服务`,
        selectedLaunchDate
      });

      await order.save();

      // 更新工具状态
      const updateData: any = {
        launchDateSelected: true,
        selectedLaunchDate,
        launchDate: selectedLaunchDate, // 同时设置launchDate
        launchOption: 'paid',
        paymentRequired: true,
        paymentAmount,
        paymentStatus: 'pending',
        orderId: order._id.toString()
      };

      // 付费工具在付费成功前始终保持draft状态
      updateData.status = 'draft';

      await Tool.findByIdAndUpdate(id, { $set: updateData });

      // 这里应该集成真实的支付系统（如Stripe、支付宝等）
      // 现在返回一个模拟的支付URL
      const paymentUrl = `/payment/checkout?orderId=${order._id}`;

      return NextResponse.json({
        success: true,
        data: {
          orderId: order._id,
          paymentUrl,
          amount: paymentAmount,
          message: tool.status === 'pending' ? getApiMessage(locale, 'payment.upgrade_order_created') : getApiMessage(locale, 'payment.order_created')
        }
      });
    }

  } catch (error) {
    console.error('Launch date selection error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}

// GET /api/tools/[id]/launch-date - 获取工具发布日期信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 如果有订单，获取订单信息
    let order = null;
    if (tool.orderId) {
      order = await Order.findById(tool.orderId);
    }

    return NextResponse.json({
      success: true,
      data: {
        tool: {
          id: tool._id,
          name: tool.name,
          status: tool.status,
          launchDateSelected: tool.launchDateSelected,
          selectedLaunchDate: tool.selectedLaunchDate,
          launchOption: tool.launchOption,
          paymentRequired: tool.paymentRequired,
          paymentStatus: tool.paymentStatus
        },
        order
      }
    });

  } catch (error) {
    console.error('Get launch date info error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}

// PATCH /api/tools/[id]/launch-date - 修改工具发布日期（仅限付费用户）
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { selectedDate } = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    if (!selectedDate) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查工具状态（只有还未发布的工具可以修改）
    if (!['pending', 'approved'].includes(tool.status)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.edit_not_allowed') },
        { status: 400 }
      );
    }

    // 检查是否已经发布
    const now = new Date();
    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= now;
    if (isPublished) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.already_published') },
        { status: 400 }
      );
    }

    const selectedLaunchDate = new Date(selectedDate);

    // 根据付费状态验证日期
    if (tool.launchOption === 'paid') {
      // 付费用户：可以选择明天及以后的任意日期
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      if (selectedLaunchDate < tomorrow) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'tools.paid_date_restriction') },
          { status: 400 }
        );
      }
    } else {
      // 免费用户：只能选择一个月后的日期
      const oneMonthLater = new Date();
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
      oneMonthLater.setHours(0, 0, 0, 0);

      const selectedDateOnly = new Date(selectedLaunchDate);
      selectedDateOnly.setHours(0, 0, 0, 0);

      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'tools.free_date_restriction') },
          { status: 400 }
        );
      }
    }

    // 更新工具的发布日期
    await Tool.findByIdAndUpdate(id, {
      $set: {
        selectedLaunchDate,
        launchDate: selectedLaunchDate // 同时更新launchDate
      }
    });

    // 如果有关联的订单，也更新订单的发布日期
    if (tool.orderId) {
      await Order.findByIdAndUpdate(tool.orderId, {
        $set: {
          selectedLaunchDate
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        message: getApiMessage(locale, 'tools.launch_date_updated'),
        selectedLaunchDate
      }
    });

  } catch (error) {
    console.error('Update launch date error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
