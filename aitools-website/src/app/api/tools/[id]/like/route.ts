import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const { id: toolId } = await params;

    // 查找工具
    const tool = await Tool.findById(toolId);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查请求体中是否有强制操作标识
    const body = await request.json().catch(() => ({}));
    const forceUnlike = body.forceUnlike === true;

    // 检查用户是否已经点赞（以tool.likedBy为准）
    const userIdStr = user._id.toString();
    const currentlyLiked = tool.likedBy.find((lb: string) => lb.toString() === userIdStr);

    let newLikedState: boolean;

    if (forceUnlike) {
      // 强制取消点赞
      newLikedState = false;
    } else {
      // 切换点赞状态
      newLikedState = !currentlyLiked;
    }

    if (newLikedState) {
      // 添加点赞
      if (!currentlyLiked) {
        tool.likedBy.push(userIdStr);
      }
      if (!user.likedTools.includes(toolId)) {
        user.likedTools.push(toolId);
      }

      tool.likes = tool.likes + 1;
    } else {
      // 取消点赞
      tool.likedBy = tool.likedBy.filter((id: string) => id.toString() !== userIdStr);
      user.likedTools = user.likedTools.filter((id: string) => id.toString() !== toolId);

      tool.likes = tool.likes - 1;
    }

    // 保存更改
    await tool.save();
    await user.save();

    return NextResponse.json({
      success: true,
      data: {
        liked: newLikedState,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Like tool error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    await dbConnect();

    const { id: toolId } = await params;
    const tool = await Tool.findById(toolId);

    if (!tool) {
      const locale = getLocaleFromRequest(request);
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    let liked = false;
    if (session?.user?.email) {
      const user = await User.findOne({ email: session.user.email });
      if (user) {
        // 以tool.likedBy为准检查点赞状态
        liked = tool.likedBy.includes(user._id.toString());
      }
    }

    // 确保likes数量与likedBy数组长度一致
    const actualLikes = tool.likedBy.length;
    if (tool.likes !== actualLikes) {
      tool.likes = actualLikes;
      await tool.save();
    }

    return NextResponse.json({
      success: true,
      data: {
        liked,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Get like status error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
