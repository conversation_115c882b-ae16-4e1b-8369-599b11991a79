import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import User from '../../../../models/User';
import nodemailer from 'nodemailer';
import crypto from 'crypto';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// 邮件传输器
const emailTransporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// POST /api/auth/send-code - 发送邮件验证码
export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const { email } = await request.json();
    const locale = getLocaleFromRequest(request);

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email || !emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }
    
    // 生成6位数验证码
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期
    
    // 查找或创建用户
    let user = await User.findOne({ email: email.toLowerCase() });
    
    if (!user) {
      // 创建新用户（未验证状态）
      user = new User({
        email: email.toLowerCase(),
        name: email.split('@')[0], // 使用邮箱前缀作为默认用户名
        emailVerified: false,
        emailVerificationToken: verificationToken,
        emailVerificationExpires: expiresAt,
      });
    } else {
      // 更新验证码
      user.emailVerificationToken = verificationToken;
      user.emailVerificationExpires = expiresAt;
    }
    
    await user.save();
    
    // 发送验证码邮件
    try {
      await emailTransporter.sendMail({
        to: email,
        from: process.env.EMAIL_FROM,
        subject: 'AI Tools Directory - 登录验证码',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #3B82F6; margin: 0;">AI Tools Directory</h1>
            </div>
            
            <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; text-align: center;">
              <h2 style="color: #1f2937; margin-bottom: 20px;">您的登录验证码</h2>
              
              <div style="background-color: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <span style="font-size: 32px; font-weight: bold; color: #3B82F6; letter-spacing: 8px;">
                  ${verificationCode}
                </span>
              </div>
              
              <p style="color: #6b7280; margin: 20px 0;">
                请在10分钟内输入此验证码完成登录
              </p>
              
              <p style="color: #ef4444; font-size: 14px; margin-top: 30px;">
                如果您没有请求此验证码，请忽略此邮件
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #9ca3af; font-size: 12px;">
              <p>此邮件由 AI Tools Directory 自动发送，请勿回复</p>
            </div>
          </div>
        `,
        text: `您的 AI Tools Directory 登录验证码是：${verificationCode}。请在10分钟内使用此验证码完成登录。`,
      });
      
      // 为了安全，我们将验证码存储在数据库中而不是直接返回
      // 这里我们临时存储验证码用于验证（实际应用中应该加密存储）
      user.emailVerificationToken = `${verificationToken}:${verificationCode}`;
      await user.save();
      
      return NextResponse.json({
        success: true,
        message: getApiMessage(locale, 'auth.code_sent'),
        token: verificationToken // 返回token用于后续验证
      });

    } catch (emailError) {
      console.error('Email sending error:', emailError);
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'auth.code_send_failed') },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Send code error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
